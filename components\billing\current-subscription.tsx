"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, CreditCard, AlertCircle, CheckCircle, XCircle } from "lucide-react";
import { authClient } from "@/lib/auth-client";
import { useState, useEffect } from "react";
import { toast } from "sonner";

interface Subscription {
  id: string;
  status: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  product: {
    name: string;
    description: string;
  };
  price: {
    amount: number;
    currency: string;
    interval: string;
  };
}

export function CurrentSubscription() {
  const { data: session } = authClient.useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [fetchingSubscription, setFetchingSubscription] = useState(true);
  
  // Fetch user's current subscription from database
  const fetchSubscription = async () => {
    if (!session?.user) return;
    
    try {
      setFetchingSubscription(true);
      const response = await fetch('/api/subscriptions/current');
      if (response.ok) {
        const data = await response.json();
        setSubscription(data.subscription);
      } else if (response.status !== 404) {
        // 404 means no subscription, which is fine
        console.error('Failed to fetch subscription');
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setFetchingSubscription(false);
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [session?.user]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: string, cancelAtPeriodEnd: boolean) => {
    if (cancelAtPeriodEnd) {
      return (
        <Badge variant="outline" className="border-orange-300 text-orange-700 dark:border-orange-700 dark:text-orange-300">
          <AlertCircle className="w-3 h-3 mr-1" />
          Cancelling
        </Badge>
      );
    }

    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case "past_due":
        return (
          <Badge variant="destructive">
            <AlertCircle className="w-3 h-3 mr-1" />
            Past Due
          </Badge>
        );
      case "canceled":
        return (
          <Badge variant="outline" className="border-red-300 text-red-700 dark:border-red-700 dark:text-red-300">
            <XCircle className="w-3 h-3 mr-1" />
            Canceled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;
    
    setIsLoading(true);
    try {
      const response = await fetch('/api/subscriptions/cancel', { 
        method: 'POST', 
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ subscriptionId: subscription.id }) 
      });
      
      if (response.ok) {
        toast.success("Subscription will be cancelled at the end of the current billing period");
        await fetchSubscription(); // Refresh subscription data
      } else {
        throw new Error('Failed to cancel subscription');
      }
    } catch (error) {
      toast.error("Failed to cancel subscription");
    } finally {
      setIsLoading(false);
    }
  };

  const handleReactivateSubscription = async () => {
    if (!subscription) return;
    
    setIsLoading(true);
    try {
      const response = await fetch('/api/subscriptions/reactivate', { 
        method: 'POST', 
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ subscriptionId: subscription.id }) 
      });
      
      if (response.ok) {
        toast.success("Subscription reactivated successfully");
        await fetchSubscription(); // Refresh subscription data
      } else {
        throw new Error('Failed to reactivate subscription');
      }
    } catch (error) {
      toast.error("Failed to reactivate subscription");
    } finally {
      setIsLoading(false);
    }
  };

  if (fetchingSubscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
            <CreditCard className="w-5 h-5 text-[#ffbe98]" />
            Current Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-neutral-200 dark:bg-neutral-800 rounded w-3/4"></div>
            <div className="h-4 bg-neutral-200 dark:bg-neutral-800 rounded w-1/2"></div>
            <div className="h-10 bg-neutral-200 dark:bg-neutral-800 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
            <CreditCard className="w-5 h-5 text-[#ffbe98]" />
            Current Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CreditCard className="w-12 h-12 text-neutral-400 mx-auto mb-3" />
            <p className="text-neutral-600 dark:text-neutral-400">
              You don't have any active subscriptions yet.
            </p>
            <p className="text-sm text-neutral-500 dark:text-neutral-500 mt-1">
              Choose a plan below to get started.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
          <CreditCard className="w-5 h-5 text-[#ffbe98]" />
          Current Subscription
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="font-semibold text-lg text-neutral-900 dark:text-neutral-200">
              {subscription.product.name}
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {subscription.product.description}
            </p>
          </div>
          {getStatusBadge(subscription.status, subscription.cancelAtPeriodEnd)}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-neutral-500 dark:text-neutral-400">Price</div>
            <div className="font-semibold text-neutral-900 dark:text-neutral-200">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: subscription.price.currency,
                minimumFractionDigits: 2,
              }).format(subscription.price.amount / 100)}
              <span className="text-sm font-normal text-neutral-500 dark:text-neutral-400">
                /{subscription.price.interval}
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-neutral-500 dark:text-neutral-400">Current Period</div>
            <div className="text-sm text-neutral-900 dark:text-neutral-200">
              {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-neutral-500 dark:text-neutral-400">Next Billing</div>
            <div className="flex items-center gap-2 text-sm text-neutral-900 dark:text-neutral-200">
              <Calendar className="w-4 h-4" />
              {formatDate(subscription.currentPeriodEnd)}
            </div>
          </div>
        </div>

        {subscription.cancelAtPeriodEnd && (
          <div className="p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  Subscription Cancelling
                </p>
                <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                  Your subscription will end on {formatDate(subscription.currentPeriodEnd)}. 
                  You'll continue to have access until then.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex gap-3 pt-4 border-t border-neutral-200 dark:border-neutral-800">
          {subscription.cancelAtPeriodEnd ? (
            <Button
              onClick={handleReactivateSubscription}
              disabled={isLoading}
              className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white"
            >
              Reactivate Subscription
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={handleCancelSubscription}
              disabled={isLoading}
              className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-950/20"
            >
              Cancel Subscription
            </Button>
          )}
          
          <Button variant="outline">
            Update Payment Method
          </Button>
          
          <Button variant="outline">
            Download Invoice
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
