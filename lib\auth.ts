import { betterAuth } from "better-auth";
import { supabaseAdapter } from "./supabase-adapter";

/**
 * Better Auth configuration using pure Supabase adapter
 * This uses a custom Supabase adapter that integrates directly with Supabase client
 * instead of using DATABASE_URL or PostgreSQL connection strings
 */
export const auth = betterAuth({
  database: supabaseAdapter({
    debugLogs: process.env.NODE_ENV === 'development',
  }),
  emailAndPassword: {
    enabled: true,
  },
  user: {
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async ({ user, newEmail, url, token }, request) => {
        // For now, we'll log the verification details
        // In production, you should implement email sending here
        console.log('Email change verification:', {
          currentEmail: user.email,
          newEmail,
          verificationUrl: url,
          token
        });

        // TODO: Implement email sending service
        // Example: await sendEmail({
        //   to: user.email,
        //   subject: 'Approve email change',
        //   html: `Click <a href="${url}">here</a> to approve the email change to ${newEmail}`
        // });
      }
    },
    deleteUser: {
      enabled: true,
      sendDeleteAccountVerification: async ({ user, url, token }, request) => {
        // For now, we'll log the verification details
        // In production, you should implement email sending here
        console.log('Account deletion verification:', {
          userEmail: user.email,
          verificationUrl: url,
          token
        });

        // TODO: Implement email sending service
        // Example: await sendEmail({
        //   to: user.email,
        //   subject: 'Confirm account deletion',
        //   html: `Click <a href="${url}">here</a> to permanently delete your account. This action cannot be undone.`
        // });
      },
      beforeDelete: async (user, request) => {
        // Log the deletion for audit purposes
        console.log('User account deletion:', {
          userId: user.id,
          email: user.email,
          deletedAt: new Date().toISOString()
        });

        // You can add additional cleanup logic here
        // Example: Delete user data from other services, cancel subscriptions, etc.
      },
      afterDelete: async (user, request) => {
        // Log successful deletion
        console.log('User account successfully deleted:', {
          userId: user.id,
          email: user.email,
          completedAt: new Date().toISOString()
        });

        // You can add post-deletion logic here
        // Example: Send confirmation email, update analytics, etc.
      }
    }
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
  },
});
